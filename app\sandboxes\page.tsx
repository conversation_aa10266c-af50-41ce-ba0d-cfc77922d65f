"use client"

import { useEffect, useState } from "react"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"

interface Sandbox { id: string; provider: "e2b"; name: string; status: string; capabilities: string[]; agentIds: string[] }

export default function SandboxesPage() {
  const [sandboxes, setSandboxes] = useState<Sandbox[]>([])
  const [name, setName] = useState("")

  const load = async () => {
    const res = await fetch('/api/v1/sandboxes')
    const json = await res.json()
    if (res.ok) setSandboxes(json.data as Sandbox[])
  }
  useEffect(() => { void load() }, [])

  const create = async () => {
    const body = { provider: 'e2b', name }
    const res = await fetch('/api/v1/sandboxes', { method: 'POST', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify(body) })
    if (res.ok) { setName(""); await load() }
  }

  return (
    <div className="p-6 space-y-6">
      <h2 className="text-xl font-semibold tracking-tight">Sandboxes</h2>

      <Card className="bg-neutral-900 border-neutral-700">
        <CardHeader>
          <CardTitle className="text-sm text-neutral-300">Create Sandbox</CardTitle>
        </CardHeader>
        <CardContent className="flex gap-2">
          <Input placeholder="Name" value={name} onChange={e => setName(e.target.value)} className="bg-neutral-800 border-neutral-700 text-white placeholder-neutral-500" />
          <Button onClick={create} disabled={!name.trim()} className="bg-red-500 hover:bg-red-600 text-white">Create</Button>
        </CardContent>
      </Card>

      <Card className="bg-neutral-900 border-neutral-700">
        <CardHeader>
          <CardTitle className="text-sm text-neutral-300">All Sandboxes</CardTitle>
        </CardHeader>
        <CardContent>
          {sandboxes.length === 0 ? (
            <p className="text-sm text-neutral-500">No sandboxes yet.</p>
          ) : (
            <ul className="space-y-2">
              {sandboxes.map(sb => (
                <li key={sb.id} className="flex items-center justify-between p-3 bg-neutral-800 rounded">
                  <div>
                    <div className="text-white text-sm font-semibold">{sb.name}</div>
                    <div className="text-xs text-neutral-400">Provider: {sb.provider.toUpperCase()} • Status: {sb.status}</div>
                  </div>
                  <div className="text-xs text-neutral-400">Agents: {sb.agentIds?.length ?? 0}</div>
                </li>
              ))}
            </ul>
          )}
        </CardContent>
      </Card>
    </div>
  )
}

