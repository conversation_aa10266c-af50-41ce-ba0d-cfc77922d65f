'use server'

import { Sandbox } from '@e2b/code-interpreter'

// In-memory store for agent sandboxes (in production, use Redis or database)
const agentSandboxes = new Map<string, string>()

export async function runInAgentSandbox(
  prevState: any,
  formData: FormData
): Promise<{ stdout?: string; stderr?: string; exitCode?: number; ok: boolean; error?: string }> {
  try {
    const agentId = formData.get('agentId') as string
    const cmd = formData.get('cmd') as string

    if (!agentId || !cmd) {
      return { ok: false, error: 'Missing agentId or command' }
    }

    // Check for E2B API key
    if (!process.env.E2B_API_KEY) {
      return { ok: false, error: 'E2B_API_KEY not configured' }
    }

    // Get or create sandbox for this agent
    let sandboxId = agentSandboxes.get(agentId)
    let sandbox: any

    if (sandboxId) {
      try {
        // Try to reconnect/connect to existing sandbox (API compatibility across versions)
        const anySandbox = Sandbox as any
        sandbox = typeof anySandbox.connect === 'function'
          ? await anySandbox.connect(sandboxId)
          : await anySandbox.reconnect(sandboxId)
      } catch {
        // If reconnection fails, create new sandbox
        {
          const template = process.env.E2B_TEMPLATE_ID || 'kali-linux-sandbox'
          sandbox = await Sandbox.create(template)
        }
        {
          const sbId = (sandbox as any)?.id ?? (sandbox as any)?.sandboxId ?? null
          if (sbId) agentSandboxes.set(agentId, sbId)
        }
      }
    } else {
      // Create new sandbox
      {
        const template = process.env.E2B_TEMPLATE_ID || 'kali-linux-sandbox'
        sandbox = await Sandbox.create(template)
      }
      {
        const sbId = (sandbox as any)?.id ?? (sandbox as any)?.sandboxId ?? null
        if (sbId) agentSandboxes.set(agentId, sbId)
      }
    }

    // Execute command
    // Support both new and legacy E2B APIs
    let result: any
    if (sandbox?.process?.start) {
      const proc = await sandbox.process.start({ cmd })
      result = await proc.wait()
    } else if (typeof sandbox?.exec === 'function') {
      result = await sandbox.exec(cmd)
    } else {
      throw new Error('Unsupported E2B sandbox execution API')
    }

    return {
      stdout: result.stdout,
      stderr: result.stderr,
      exitCode: result.exitCode,
      ok: result.exitCode === 0,
    }
  } catch (error: any) {
    return {
      ok: false,
      error: error?.message || 'Failed to execute command',
    }
  }
}
