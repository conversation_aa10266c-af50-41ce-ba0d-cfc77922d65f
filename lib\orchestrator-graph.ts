import { MemorySaver, addMessages, entrypoint } from "@langchain/langgraph"
import { MessagesAnnotation, StateGraph } from "@langchain/langgraph"
import { ChatOpenAI } from "@langchain/openai"
import { tool } from "@langchain/core/tools"
import { z } from "zod"
import type { BaseMessageLike } from "@langchain/core/messages"
import { listAgents } from "@/lib/agents-store"
import { listSwarms } from "@/lib/platform-store"

// Planning tool (Deep Agent Pillar I) - no-op persister via MemorySaver
const planningSchema = z.object({ tasks: z.array(z.string()) })
export const createOrUpdatePlan = tool(async ({ tasks }: { tasks: string[] }) => {
  return { ok: true, tasks }
}, {
  name: "create_or_update_plan",
  description: "Create or update the current execution plan (list of tasks)",
  schema: planningSchema,
})

// Simple list tools to prove plumbing
export const listAgentsTool = tool(async () => ({ agents: listAgents() }), {
  name: "list_agents",
  description: "List all agents with their status",
  schema: z.object({}),
})

export const listSwarmsTool = tool(async () => ({ swarms: listSwarms() }), {
  name: "list_swarms",
  description: "List all swarms",
  schema: z.object({}),
})

export function buildOrchestrator() {
  const tools = [createOrUpdatePlan, listAgentsTool, listSwarmsTool]
  const llm = new ChatOpenAI({ model: process.env.OPENAI_MODEL ?? "gpt-4o-mini" })
  const modelWithTools = llm.bindTools(tools)

  // Minimal ReAct loop using the functional entrypoint
  const agent = entrypoint({
    checkpointer: new MemorySaver(),
    name: "orchestrator",
  }, async (messages: BaseMessageLike[]) => {
    // Call model, handle tools until final
    let response = await modelWithTools.invoke(messages)
    while (response.tool_calls?.length) {
      const outputs = [] as any[]
      for (const call of response.tool_calls) {
        const selectedTool = tools.find(t => t.name === call.name)!
        const toolResult = await (selectedTool as any).invoke((call as any).args ?? {})
        outputs.push(toolResult)
      }
      messages = addMessages(messages, [response, ...outputs])
      response = await modelWithTools.invoke(messages)
    }
    return entrypoint.final({ value: response, save: addMessages(messages, response) })
  })

  return agent
}

