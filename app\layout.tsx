import type React from "react"
import type { <PERSON><PERSON><PERSON> } from "next"
import { <PERSON><PERSON><PERSON>_Mono as <PERSON><PERSON><PERSON><PERSON><PERSON> } from "next/font/google"
import "./globals.css"

const geistMono = GeistMono({ subsets: ["latin"] })

export const metadata: Metadata = {
  title: "Agentic Swarm Platform",
  description: "Multi-agent orchestration with swarms, MCP, sandboxes, and automations",
  generator: 'v0.dev'
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="es">
      <body className={`${geistMono.className} bg-black text-white antialiased`}>{children}</body>
    </html>
  )
}
