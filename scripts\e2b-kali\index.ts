import 'dotenv/config'
import { Sandbox } from '@e2b/code-interpreter'

/**
 * Quick verifier script:
 * - Creates a sandbox from your Kali template
 * - Runs `nmap --version`
 *
 * Usage:
 *   npx tsx scripts/e2b-kali/index.ts
 *
 * Env:
 *   E2B_API_KEY=your_api_key
 *   E2B_TEMPLATE_ID=optional_override  (defaults to "kali-linux-sandbox")
 */

async function main() {
  const apiKey = process.env.E2B_API_KEY
  if (!apiKey) {
    console.error('Missing E2B_API_KEY')
    process.exit(1)
  }

  const templateId = process.env.E2B_TEMPLATE_ID || 'kali-linux-sandbox'
  console.log(`Creating sandbox from template: ${templateId}`)

  const sandbox = await Sandbox.create(templateId)
  console.log('✅ Sandbox created! Verifying Kali tools...')

  // Support both new and legacy E2B APIs
  if ((sandbox as any)?.process?.start) {
    const proc = await (sandbox as any).process.start({
      cmd: ['bash', '-lc', 'nmap --version'],
      onStdout: (d: any) => process.stdout.write((d?.line ?? '') + '\n'),
      onStderr: (d: any) => process.stderr.write((d?.line ?? '') + '\n'),
    })
    const res = await proc.wait()
    console.log('\n---')
    console.log(`Exit code: ${res?.exitCode}`)
  } else if (typeof (sandbox as any).exec === 'function') {
    const res = await (sandbox as any).exec(['bash', '-lc', 'nmap --version'])
    console.log('\n---')
    console.log(`Exit code: ${res?.exitCode}`)
  }

  if (typeof (sandbox as any).close === 'function') {
    await (sandbox as any).close()
  }
}

main().catch((err) => {
  console.error(err)
  process.exit(1)
})
