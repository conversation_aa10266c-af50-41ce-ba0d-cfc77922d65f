"use client"

import { useState } from "react"
import { ChevronRight, Monitor, Settings, Users, Bell, RefreshCw, Brain, Server, Terminal } from 'lucide-react'
import { <PERSON><PERSON> } from "@/components/ui/button"
import CommandCenterPage from "./command-center/page"
import AgentNetworkPage from "./agent-network/page"
import OrchestratorEmbedded from "./orchestrator/embedded"
import MCPServersPage from "./mcp-servers/page"
import SandboxesPage from "./sandboxes/page"
import SwarmsPage from "./swarms/page"
import AutomationsPage from "./automations/page"

export default function AgenticPlatform() {
  const [activeSection, setActiveSection] = useState("overview")
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false)

  const labelMap: Record<string, string> = {
    overview: "DASHBOARD",
    agents: "AGENTS",
    mcp: "MCP SERVERS",
    sandboxes: "SANDBOXES",
    swarms: "SWARMS",
    automations: "AUTOMATIONS",
    orchestrator: "ORCHESTRATOR",
  }

  return (
    <div className="flex h-screen">
      {/* Sidebar */}
      <div
        className={`${sidebarCollapsed ? "w-16" : "w-70"} bg-neutral-900 border-r border-neutral-700 transition-all duration-300 fixed md:relative z-50 md:z-auto h-full md:h-auto ${!sidebarCollapsed ? "md:block" : ""}`}
      >
        <div className="p-4">
          <div className="flex items-center justify-between mb-8">
            <div className={`${sidebarCollapsed ? "hidden" : "block"}`}>
              <h1 className="text-white font-bold text-lg tracking-wider">
                AP3<span className="text-red-500">X</span>
              </h1>
              <p className="text-neutral-500 text-xs">v2.1.7</p>
            </div>
            <Button
              variant="ghost"
              size="icon"
              onClick={() => setSidebarCollapsed(!sidebarCollapsed)}
              className="text-neutral-400 hover:text-red-500"
            >
              <ChevronRight
                className={`w-4 h-4 sm:w-5 sm:h-5 transition-transform ${sidebarCollapsed ? "" : "rotate-180"}`}
              />
            </Button>
          </div>

          <nav className="space-y-2">
            {[
              { id: "overview", icon: Monitor, label: "DASHBOARD" },
              { id: "agents", icon: Users, label: "AGENTS" },
              { id: "mcp", icon: Server, label: "MCP SERVERS" },
              { id: "sandboxes", icon: Terminal, label: "SANDBOXES" },
              { id: "swarms", icon: Users, label: "SWARMS" },
              { id: "automations", icon: Settings, label: "AUTOMATIONS" },
              { id: "orchestrator", icon: Brain, label: "ORCHESTRATOR" },
            ].map((item) => (
              <button
                key={item.id}
                onClick={() => setActiveSection(item.id)}
                className={`w-full flex items-center gap-3 p-3 rounded transition-colors ${
                  activeSection === item.id
                    ? "bg-red-500 text-white"
                    : "text-neutral-400 hover:text-white hover:bg-neutral-800"
                }`}
              >
                <item.icon className="w-5 h-5 md:w-5 md:h-5 sm:w-6 sm:h-6" />
                {!sidebarCollapsed && <span className="text-sm font-medium">{item.label}</span>}
              </button>
            ))}
          </nav>

          {!sidebarCollapsed && (
            <div className="mt-8 p-4 bg-neutral-800 border border-neutral-700 rounded">
              <div className="flex items-center gap-2 mb-2">
                <div className="w-2 h-2 bg-white rounded-full animate-pulse"></div>
                <span className="text-xs text-white">SYSTEM ONLINE</span>
              </div>
              <div className="text-xs text-neutral-500">
                <div>UPTIME: 72:14:33</div>
                <div>AGENTS: 847 ACTIVE</div>
                <div>ORCHESTRATOR: READY</div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Mobile Overlay */}
      {!sidebarCollapsed && (
        <div className="fixed inset-0 bg-black/50 z-40 md:hidden" onClick={() => setSidebarCollapsed(true)} />
      )}

      {/* Main Content */}
      <div className={`flex-1 flex flex-col ${!sidebarCollapsed ? "md:ml-0" : ""}`}>
        {/* Top Toolbar */}
        <div className="h-16 bg-neutral-800 border-b border-neutral-700 flex items-center justify-between px-6">
          <div className="flex items-center gap-4">
            <div className="text-sm text-neutral-400">
              {"AGENTIC PLATFORM / "}
              <span className="text-red-500">{labelMap[activeSection] ?? "DASHBOARD"}</span>
            </div>
          </div>
          <div className="flex items-center gap-4">
            <div className="text-xs text-neutral-500">LAST UPDATE: 05/06/2025 20:00 UTC</div>
            <Button variant="ghost" size="icon" className="text-neutral-400 hover:text-red-500">
              <Bell className="w-4 h-4" />
            </Button>
            <Button variant="ghost" size="icon" className="text-neutral-400 hover:text-red-500">
              <RefreshCw className="w-4 h-4" />
            </Button>
          </div>
        </div>

        {/* Dashboard Content */}
        <div className="flex-1 overflow-auto">
          {activeSection === "overview" && <CommandCenterPage />}
          {activeSection === "agents" && <AgentNetworkPage />}
          {activeSection === "mcp" && <MCPServersPage />}
          {activeSection === "sandboxes" && <SandboxesPage />}
          {activeSection === "swarms" && <SwarmsPage />}
          {activeSection === "automations" && <AutomationsPage />}
          {activeSection === "orchestrator" && <OrchestratorEmbedded />}
        </div>
      </div>
    </div>
  )
}
