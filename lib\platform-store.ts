// In-memory repositories for the agentic platform (MVP)
export type ID = string

export type Sandbox = {
  id: ID
  provider: "e2b"
  name: string
  status: "ready" | "busy" | "error"
  capabilities: string[]
  agentIds: ID[]
}

export type MCPServer = {
  id: ID
  name: string
  transport: "stdio" | "sse"
  command?: string
  args?: string[]
  url?: string
  tools: string[]
  status: "online" | "offline" | "unknown"
}

export type ToolAutomation = {
  id: ID
  name: string
  kind: "tool" | "automation"
  source: "internal" | "external" | "mcp"
  schema?: unknown
}

export type Swarm = {
  id: ID
  name: string
  memberAgentIds: ID[]
}

export const sandboxes: Sandbox[] = []
export const mcpServers: MCPServer[] = []
export const tools: ToolAutomation[] = []
export const swarms: Swarm[] = []


// --- Swarm CRUD (MVP) ---
export function listSwarms(): Swarm[] {
  return swarms
}

export function createSwarm(name: string): Swarm {
  const s: Swarm = { id: crypto.randomUUID(), name, memberAgentIds: [] }
  swarms.push(s)
  return s
}

export function addMemberToSwarm(swarmId: ID, agentId: ID): { success: boolean; message?: string } {
  const s = swarms.find(x => x.id === swarmId)
  if (!s) return { success: false, message: 'Swarm not found' }
  if (!s.memberAgentIds.includes(agentId)) s.memberAgentIds.push(agentId)
  return { success: true }
}

export function removeMemberFromSwarm(swarmId: ID, agentId: ID): { success: boolean; message?: string } {
  const s = swarms.find(x => x.id === swarmId)
  if (!s) return { success: false, message: 'Swarm not found' }
  s.memberAgentIds = s.memberAgentIds.filter(id => id !== agentId)
  return { success: true }
}


// --- MCP Servers ---
export function listMCPServers() {
  return mcpServers
}

export function createMCPServer(input: Omit<MCPServer, 'id' | 'status'> & Partial<Pick<MCPServer, 'status'>>) {
  const server: MCPServer = {
    id: crypto.randomUUID(),
    status: input.status ?? 'unknown',
    name: input.name,
    transport: input.transport,
    command: input.command,
    args: input.args,
    url: input.url,
    tools: input.tools ?? []
  }
  mcpServers.push(server)
  return server
}

// --- Sandboxes ---
export function listSandboxes() {
  return sandboxes
}

export function createSandbox(input: Omit<Sandbox, 'id' | 'status' | 'agentIds'> & Partial<Pick<Sandbox, 'status' | 'agentIds'>>) {
  const sb: Sandbox = {
    id: crypto.randomUUID(),
    provider: input.provider,
    name: input.name,
    status: input.status ?? 'ready',
    capabilities: input.capabilities ?? [],
    agentIds: input.agentIds ?? []
  }
  sandboxes.push(sb)
  return sb
}

// --- Tools/Automations ---
export function listTools() {
  return tools.filter(t => t.kind === 'tool')
}
export function listAutomations() {
  return tools.filter(t => t.kind === 'automation')
}
export function createToolAutomation(input: Omit<ToolAutomation, 'id'>) {
  const ta: ToolAutomation = { id: crypto.randomUUID(), ...input }
  tools.push(ta)
  return ta
}
