"use client"

import { useEffect, useState } from "react"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"

type Swarm = { id: string; name: string; memberAgentIds: string[] }

export default function SwarmsPage() {
  const [swarms, setSwarms] = useState<Swarm[]>([])
  const [name, setName] = useState("")
  const [isLoading, setIsLoading] = useState(false)

  const load = async () => {
    try {
      const res = await fetch('/api/v1/swarms')
      const json = await res.json()
      if (res.ok) setSwarms(json.data as Swarm[])
    } catch (e) {
      console.error('Failed to load swarms', e)
    }
  }

  useEffect(() => { void load() }, [])

  const create = async () => {
    if (!name.trim()) return
    setIsLoading(true)
    try {
      const res = await fetch('/api/v1/swarms', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ name: name.trim() })
      })
      if (res.ok) {
        setName("")
        await load()
      }
    } catch (e) {
      console.error('Failed to create swarm', e)
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="p-6 space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-semibold tracking-tight">Swarms</h2>
      </div>

      <Card className="bg-neutral-900 border-neutral-700">
        <CardHeader>
          <CardTitle className="text-sm text-neutral-300">Create a Swarm</CardTitle>
        </CardHeader>
        <CardContent className="flex gap-2">
          <Input
            placeholder="Swarm name"
            value={name}
            onChange={(e) => setName(e.target.value)}
            className="bg-neutral-800 border-neutral-700 text-white placeholder-neutral-500"
          />
          <Button onClick={create} disabled={isLoading || !name.trim()} className="bg-red-500 hover:bg-red-600 text-white">
            Create
          </Button>
        </CardContent>
      </Card>

      <Card className="bg-neutral-900 border-neutral-700">
        <CardHeader>
          <CardTitle className="text-sm text-neutral-300">All Swarms</CardTitle>
        </CardHeader>
        <CardContent>
          {swarms.length === 0 ? (
            <p className="text-sm text-neutral-500">No swarms yet.</p>
          ) : (
            <ul className="space-y-2">
              {swarms.map((s) => (
                <li key={s.id} className="flex items-center justify-between p-3 bg-neutral-800 rounded">
                  <div>
                    <div className="text-white text-sm font-semibold">{s.name}</div>
                    <div className="text-xs text-neutral-400">Members: {s.memberAgentIds?.length ?? 0}</div>
                  </div>
                </li>
              ))}
            </ul>
          )}
        </CardContent>
      </Card>
    </div>
  )
}

