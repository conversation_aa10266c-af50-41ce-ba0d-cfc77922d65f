"use client"

import OrchestratorChat from "@/components/orchestrator-chat"

export default function OrchestratorPage() {
  return (
    <div className="min-h-screen bg-neutral-950 p-6">
      <div className="max-w-6xl mx-auto space-y-6">
        <header className="text-center space-y-2">
          <h1 className="text-3xl font-bold text-white tracking-wider">
            AP3<span className="text-red-500">X</span> ORCHESTRATOR
          </h1>
          <p className="text-neutral-400">
            Central command agent coordinating and controlling all sub-agents
          </p>
        </header>

        <OrchestratorChat />
      </div>
    </div>
  )
}
