"use client"

import OrchestratorChat from "@/components/orchestrator-chat"

export default function OrchestratorEmbedded() {
  return (
    <div className="p-6 space-y-6">
      <header className="flex flex-col md:flex-row md:items-center md:justify-between gap-3">
        <div>
          <h2 className="text-2xl font-bold tracking-wider">ORCHESTRATOR</h2>
          <p className="text-sm text-neutral-400">
            Orchestrates agents, swarms, MCP servers, sandboxes, and automations.
          </p>
        </div>
        <div className="text-xs text-neutral-500">
          Model: <span className="font-mono">OPENROUTER_MODEL</span> or default{" "}
          <span className="font-mono text-red-500">anthropic/claude-sonnet-4</span>
        </div>
      </header>

      <OrchestratorChat />
    </div>
  )
}
