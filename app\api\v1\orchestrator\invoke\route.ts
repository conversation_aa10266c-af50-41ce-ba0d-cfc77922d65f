import { NextRequest } from "next/server"
import { buildOrchestrator } from "@/lib/orchestrator-graph"

export async function POST(req: NextRequest) {
  try {
    const body = await req.json().catch(() => ({}))
    const messages = Array.isArray(body?.messages) ? body.messages : [{ role: 'user', content: 'Plan next steps' }]
    const agent = buildOrchestrator()
    const result = await agent.invoke(messages as any)
    return Response.json({ data: result })
  } catch (e: any) {
    return Response.json({ error: e?.message ?? 'Failed to invoke orchestrator' }, { status: 500 })
  }
}

