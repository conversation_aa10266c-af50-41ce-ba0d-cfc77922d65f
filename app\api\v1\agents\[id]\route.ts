import { getAgent, setAgentStatus, updateAgentAssignments } from "@/lib/agents-store"

export async function GET(_req: Request, ctx: { params: Promise<{ id: string }> }) {
  const { id } = await ctx.params
  const agent = getAgent(id)
  if (!agent) return Response.json({ error: "Not found" }, { status: 404 })
  return Response.json({ data: agent })
}

export async function PATCH(req: Request, ctx: { params: Promise<{ id: string }> }) {
  const { id } = await ctx.params
  const agent = getAgent(id)
  if (!agent) return Response.json({ error: "Not found" }, { status: 404 })

  const body = await req.json().catch(() => ({}))
  // Status update
  if (body?.status) {
    const result = setAgentStatus(id, body.status)
    if (!result.success) return Response.json({ error: result.message }, { status: 400 })
  }

  // Assignment updates
  const { sandboxId, mcpServerIds, toolIds, automationIds } = body ?? {}
  if (sandboxId !== undefined || mcpServerIds !== undefined || toolIds !== undefined || automationIds !== undefined) {
    const result2 = updateAgentAssignments(id, { sandboxId, mcpServerIds, toolIds, automationIds })
    if (!result2.success) return Response.json({ error: result2.message }, { status: 400 })
  }

  return Response.json({ data: getAgent(id) })
}

