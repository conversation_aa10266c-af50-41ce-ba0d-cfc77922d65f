"use client"

import { useEffect, useState } from "react"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"

interface MCPServer { id: string; name: string; transport: "stdio" | "sse"; url?: string; command?: string; args?: string[]; status: string; tools: string[] }

export default function MCPServersPage() {
  const [servers, setServers] = useState<MCPServer[]>([])
  const [name, setName] = useState("")
  const [transport, setTransport] = useState<"stdio" | "sse">("stdio")
  const [url, setUrl] = useState("")
  const [command, setCommand] = useState("")
  const [args, setArgs] = useState("")

  const load = async () => {
    const res = await fetch('/api/v1/mcp-servers')
    const json = await res.json()
    if (res.ok) setServers(json.data as MCPServer[])
  }
  useEffect(() => { void load() }, [])

  const create = async () => {
    const body: any = { name, transport }
    if (transport === 'sse') body.url = url
    else if (transport === 'stdio') {
      body.command = command
      body.args = args ? args.split(',').map(s => s.trim()).filter(Boolean) : []
    }
    const res = await fetch('/api/v1/mcp-servers', {
      method: 'POST', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify(body)
    })
    if (res.ok) {
      setName(""); setUrl(""); setCommand(""); setArgs("")
      await load()
    }
  }

  return (
    <div className="p-6 space-y-6">
      <h2 className="text-xl font-semibold tracking-tight">MCP Servers</h2>

      <Card className="bg-neutral-900 border-neutral-700">
        <CardHeader>
          <CardTitle className="text-sm text-neutral-300">Register MCP Server</CardTitle>
        </CardHeader>
        <CardContent className="grid sm:grid-cols-2 gap-2 items-center">
          <Input placeholder="Name" value={name} onChange={e => setName(e.target.value)} className="bg-neutral-800 border-neutral-700 text-white placeholder-neutral-500" />
          <div className="flex gap-2">
            <Button onClick={() => setTransport('stdio')} className={`text-white ${transport==='stdio'?'bg-red-500 hover:bg-red-600':'bg-neutral-800 hover:bg-neutral-700'}`}>STDIO</Button>
            <Button onClick={() => setTransport('sse')} className={`text-white ${transport==='sse'?'bg-red-500 hover:bg-red-600':'bg-neutral-800 hover:bg-neutral-700'}`}>SSE</Button>
          </div>
          {transport === 'sse' ? (
            <Input placeholder="SSE URL" value={url} onChange={e => setUrl(e.target.value)} className="bg-neutral-800 border-neutral-700 text-white placeholder-neutral-500" />
          ) : (
            <>
              <Input placeholder="Command" value={command} onChange={e => setCommand(e.target.value)} className="bg-neutral-800 border-neutral-700 text-white placeholder-neutral-500" />
              <Input placeholder="Args (comma-separated)" value={args} onChange={e => setArgs(e.target.value)} className="bg-neutral-800 border-neutral-700 text-white placeholder-neutral-500" />
            </>
          )}
          <div className="sm:col-span-2 flex justify-end">
            <Button onClick={create} disabled={!name.trim()} className="bg-red-500 hover:bg-red-600 text-white">Register</Button>
          </div>
        </CardContent>
      </Card>

      <Card className="bg-neutral-900 border-neutral-700">
        <CardHeader>
          <CardTitle className="text-sm text-neutral-300">Servers</CardTitle>
        </CardHeader>
        <CardContent>
          {servers.length === 0 ? (
            <p className="text-sm text-neutral-500">No registered servers.</p>
          ) : (
            <ul className="space-y-2">
              {servers.map(s => (
                <li key={s.id} className="flex items-center justify-between p-3 bg-neutral-800 rounded">
                  <div>
                    <div className="text-white text-sm font-semibold">{s.name}</div>
                    <div className="text-xs text-neutral-400">{s.transport.toUpperCase()} {s.url || s.command}</div>
                  </div>
                  <div className="text-xs text-neutral-400">Tools: {s.tools?.length ?? 0}</div>
                </li>
              ))}
            </ul>
          )}
        </CardContent>
      </Card>
    </div>
  )
}

