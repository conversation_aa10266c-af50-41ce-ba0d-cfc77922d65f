"use client"

import { useEffect, useRef, useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent } from "@/components/ui/card"
import { <PERSON>, Brain, Zap, Bo<PERSON>, User } from 'lucide-react'

type ChatMessage = {
  id: string
  role: "user" | "assistant" | "system"
  content: string
}

export default function OrchestratorChat() {
  const [messages, setMessages] = useState<ChatMessage[]>([])
  const [input, setInput] = useState("")
  const [isLoading, setIsLoading] = useState(false)
  const bottomRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    bottomRef.current?.scrollIntoView({ behavior: "smooth" })
  }, [messages, isLoading])

  const sendMessage = async (text: string) => {
    if (!text.trim()) return
    
    const userMsg: ChatMessage = { 
      id: crypto.randomUUID(), 
      role: "user", 
      content: text.trim() 
    }
    
    setMessages((prev) => [...prev, userMsg])
    setInput("")
    setIsLoading(true)
    
    try {
      const res = await fetch("/api/orchestrator-json", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          messages: [...messages, userMsg].map(({ role, content }) => ({ role, content })),
        }),
      })
      
      if (!res.ok) {
        const errorData = await res.json().catch(() => ({ error: 'Unknown error' }))
        throw new Error(errorData.error || `HTTP ${res.status}`)
      }
      
      const data = await res.json()
      const assistantMsg: ChatMessage = {
        id: crypto.randomUUID(),
        role: "assistant",
        content: data.text || "(no response)",
      }
      
      setMessages((prev) => [...prev, assistantMsg])
    } catch (e: any) {
      console.error('Chat error:', e)
      const assistantMsg: ChatMessage = {
        id: crypto.randomUUID(),
        role: "assistant",
        content: `Error: ${e?.message ?? "Failed to get response"}`,
      }
      setMessages((prev) => [...prev, assistantMsg])
    } finally {
      setIsLoading(false)
    }
  }

  const onSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    void sendMessage(input)
  }

  const quickPrompts = [
    "List all agents and their current status",
    "Show me agents at risk and propose mitigation steps",
    "Create a new operation in Tokyo with 3 agents and high priority",
    "Update agent G-078W status to standby",
  ]

  return (
    <div className="grid grid-rows-[1fr_auto] h-[78vh] gap-4">
      <Card className="bg-neutral-900 border-neutral-800 overflow-hidden">
        <CardContent className="p-0">
          <div className="h-[60vh] overflow-y-auto p-4 space-y-4">
            {messages.length === 0 && (
              <div className="rounded border border-neutral-800 p-4 bg-neutral-950">
                <div className="flex items-center gap-2 mb-2 text-neutral-300">
                  <Brain className="w-4 h-4" />
                  <span className="text-sm">Orchestrator ready</span>
                </div>
                <p className="text-xs text-neutral-400">
                  Ask the Orchestrator to coordinate agents, set statuses, and plan operations. Tool calls will be
                  executed on your behalf.
                </p>

                <div className="mt-4 grid grid-cols-1 md:grid-cols-2 gap-2">
                  {quickPrompts.map((q) => (
                    <button
                      key={q}
                      onClick={() => void sendMessage(q)}
                      className="text-left text-xs p-3 rounded bg-neutral-900 border border-neutral-800 hover:bg-neutral-800 transition"
                    >
                      <div className="flex items-center gap-2 text-neutral-300">
                        <Zap className="w-3 h-3" />
                        <span className="font-medium">Quick prompt</span>
                      </div>
                      <div className="mt-1 text-neutral-400">{q}</div>
                    </button>
                  ))}
                </div>
              </div>
            )}

            {messages.map((m) => (
              <div key={m.id} className="flex gap-3">
                <div
                  className={`mt-1 p-1 rounded-full border ${
                    m.role === "user" ? "border-neutral-700" : "border-red-500/40"
                  }`}
                >
                  {m.role === "user" ? (
                    <User className="w-4 h-4 text-neutral-400" />
                  ) : (
                    <Bot className="w-4 h-4 text-red-500" />
                  )}
                </div>
                <div
                  className={`flex-1 rounded p-3 text-sm leading-relaxed whitespace-pre-wrap ${
                    m.role === "user"
                      ? "bg-neutral-900 border border-neutral-800 text-neutral-200"
                      : "bg-neutral-950 border border-neutral-800 text-neutral-100"
                  }`}
                >
                  {m.content}
                </div>
              </div>
            ))}
            
            {isLoading && (
              <div className="flex gap-3">
                <div className="mt-1 p-1 rounded-full border border-red-500/40">
                  <Bot className="w-4 h-4 text-red-500" />
                </div>
                <div className="flex-1 rounded p-3 text-sm bg-neutral-950 border border-neutral-800 text-neutral-100">
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse"></div>
                    <span>Orchestrator processing...</span>
                  </div>
                </div>
              </div>
            )}
            
            <div ref={bottomRef} />
          </div>
        </CardContent>
      </Card>

      <form onSubmit={onSubmit} className="flex gap-2 items-center" aria-label="Send a message to the Orchestrator agent">
        <Input
          value={input}
          onChange={(e) => setInput(e.target.value)}
          placeholder="Message the Orchestrator..."
          className="flex-1 bg-neutral-900 border-neutral-800 text-white placeholder-neutral-500"
          disabled={isLoading}
        />
        <Button 
          type="submit" 
          className="bg-red-500 hover:bg-red-600 text-white" 
          disabled={isLoading || !input.trim()}
        >
          <Send className="w-4 h-4" />
          <span className="sr-only">Send</span>
        </Button>
      </form>
    </div>
  )
}
