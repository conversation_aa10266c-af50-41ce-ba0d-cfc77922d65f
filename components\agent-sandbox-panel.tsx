'use client'

import { useActionState, useEffect, useRef, useState, useTransition } from 'react'
import { runInAgentSandbox } from '@/app/actions/e2b'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { AlertTriangle, TerminalSquare, Play, Loader2 } from 'lucide-react'
import { cn } from '@/lib/utils'

type AgentSandboxPanelProps = {
  agentId: string
  codename: string
  onClose?: () => void
}

export default function AgentSandboxPanel({
  agentId = 'UNKNOWN',
  codename = 'UNKNOWN',
  onClose,
}: AgentSandboxPanelProps) {
  const [cmd, setCmd] = useState('nmap -V')
  const [history, setHistory] = useState<
    { cmd: string; stdout?: string; stderr?: string; exitCode?: number; ok: boolean }[]
  >([])
  const [state, action, isPending] = useActionState(runInAgentSandbox, null)
  const [isStreaming, startTransition] = useTransition()
  const outRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    if (state && (state.stdout || state.stderr || state.error)) {
      setHistory((prev) => [
        ...prev,
        {
          cmd,
          stdout: state.stdout,
          stderr: state.stderr || state.error,
          exitCode: state.exitCode,
          ok: !!state.ok,
        },
      ])
    }
  }, [state])

  useEffect(() => {
    outRef.current?.scrollTo({ top: outRef.current.scrollHeight, behavior: 'smooth' })
  }, [history, isPending])

  const onRun = (formData: FormData) => {
    // attach command and agentId
    formData.set('agentId', agentId)
    formData.set('cmd', cmd.trim())
  }

  return (
    <Card className="bg-neutral-900 border-neutral-700">
      <CardHeader className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2">
        <div className="space-y-1">
          <CardTitle className="text-sm font-bold tracking-wider text-white">
            Sandbox for {codename} <span className="font-mono text-neutral-400">({agentId})</span>
          </CardTitle>
          <div className="flex items-center gap-2">
            <Badge className="bg-white/20 text-white">Isolated</Badge>
            <Badge className="bg-red-500/20 text-red-500">Kali tools default</Badge>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <TerminalSquare className="w-5 h-5 text-neutral-400" />
          <span className="text-xs text-neutral-400">Run safe commands inside the agent's sandbox</span>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        <form action={action} onSubmit={(e) => onRun(new FormData(e.currentTarget))}>
          <div className="flex gap-2">
            <Input
              value={cmd}
              onChange={(e) => setCmd(e.target.value)}
              placeholder="Command to run, e.g. nmap scanme.nmap.org"
              className="bg-neutral-800 border-neutral-700 text-white placeholder-neutral-500"
            />
            <Button
              type="submit"
              className="bg-red-500 hover:bg-red-600 text-white"
              disabled={isPending || isStreaming}
              onClick={(e) => {
                // Inject the agentId/cmd into the form data via hidden inputs
                const form = (e.currentTarget as HTMLButtonElement).form
                if (form) {
                  const fd = new FormData(form)
                  onRun(fd)
                }
              }}
            >
              {isPending ? <Loader2 className="w-4 h-4 animate-spin" /> : <Play className="w-4 h-4" />}
            </Button>
          </div>
          {/* Hidden fields used by the server action */}
          <input type="hidden" name="agentId" value={agentId} />
          <input type="hidden" name="cmd" value={cmd} />
        </form>

        {(state && state.error && !state.ok) ? (
          <div className="flex items-center gap-2 text-red-500 text-sm">
            <AlertTriangle className="w-4 h-4" />
            <span>{state.error}</span>
          </div>
        ) : null}

        <div
          ref={outRef}
          className="w-full h-64 overflow-auto rounded border border-neutral-800 bg-black/70 p-3 font-mono text-xs text-neutral-200"
        >
          {history.length === 0 && (
            <div className="text-neutral-500">
              {"> Sandbox ready. Try commands like 'nmap -V', 'whoami', 'ls -la', or 'nikto -Help'."}
            </div>
          )}

          {history.map((h, idx) => (
            <div key={idx} className="mb-4">
              <div className="text-red-500">{`> ${h.cmd}`}</div>
              {h.stdout && <pre className="whitespace-pre-wrap text-neutral-200">{h.stdout}</pre>}
              {h.stderr && h.stderr.trim() && (
                <pre className="whitespace-pre-wrap text-red-400">{h.stderr}</pre>
              )}
              <div className={cn('text-[10px] mt-1', h.ok ? 'text-neutral-500' : 'text-red-500')}>
                {`exit ${h.exitCode ?? 0}`}
              </div>
            </div>
          ))}

          {(isPending || isStreaming) && <div className="text-neutral-500">{'> Running...'}</div>}
        </div>

        <div className="text-[11px] text-neutral-500">
          Safety tip: You can enforce an allowlist of commands server-side to limit what can be executed. This panel runs
          inside an isolated E2B sandbox per agent.
        </div>
      </CardContent>
    </Card>
  )
}
