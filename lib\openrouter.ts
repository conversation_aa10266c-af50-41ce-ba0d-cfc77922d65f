import { createOpenAI } from '@ai-sdk/openai'

export function getOpenRouter() {
  const apiKey = process.env.OPENROUTER_API_KEY
  if (!apiKey) {
    throw new Error('OPENROUTER_API_KEY environment variable is required')
  }

  return createOpenAI({
    apiKey,
    baseURL: 'https://openrouter.ai/api/v1',
  })
}

export const DEFAULT_OPENROUTER_MODEL = process.env.OPENROUTER_MODEL || 'anthropic/claude-3-sonnet'
