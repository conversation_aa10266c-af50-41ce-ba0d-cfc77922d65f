import { NextRequest } from "next/server"

// MVP no-op planning endpoint for Deep Agent Pillar I
// Accepts { tasks: string[] } and echoes back with an id and timestamp
export async function POST(req: NextRequest) {
  try {
    const { tasks } = await req.json()
    if (!Array.isArray(tasks)) {
      return Response.json({ error: "tasks must be an array of strings" }, { status: 400 })
    }
    const plan = {
      id: crypto.randomUUID(),
      tasks,
      createdAt: new Date().toISOString(),
    }
    return Response.json({ data: plan }, { status: 201 })
  } catch (e: any) {
    return Response.json({ error: e?.message ?? "Failed to create plan" }, { status: 500 })
  }
}

