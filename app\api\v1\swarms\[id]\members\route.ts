import { addMemberToSwarm, removeMember<PERSON>romSwarm } from "@/lib/platform-store"

export async function POST(req: Request, ctx: { params: Promise<{ id: string }> }) {
  try {
    const { id } = await ctx.params
    const { agentId } = await req.json()
    if (!agentId) return Response.json({ error: 'agentId is required' }, { status: 400 })
    const result = addMemberToSwarm(id, agentId)
    if (!result.success) return Response.json({ error: result.message }, { status: 404 })
    return Response.json({ data: { success: true } }, { status: 201 })
  } catch (e: any) {
    return Response.json({ error: e?.message ?? 'Failed to add member' }, { status: 500 })
  }
}

export async function DELETE(req: Request, ctx: { params: Promise<{ id: string }> }) {
  try {
    const { id } = await ctx.params
    const { agentId } = await req.json()
    if (!agentId) return Response.json({ error: 'agentId is required' }, { status: 400 })
    const result = removeMemberFromSwarm(id, agentId)
    if (!result.success) return Response.json({ error: result.message }, { status: 404 })
    return Response.json({ data: { success: true } })
  } catch (e: any) {
    return Response.json({ error: e?.message ?? 'Failed to remove member' }, { status: 500 })
  }
}

