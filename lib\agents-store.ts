// Simple in-memory store for agents and operations
// In production, this would be backed by a database

export type AgentStatus = 'active' | 'standby' | 'training' | 'compromised'

export type Agent = {
  id: string
  name: string
  status: AgentStatus
  location: string
  lastSeen: string
  missions: number
  risk: 'critical' | 'high' | 'medium' | 'low'
  // assignments (MVP optional)
  sandboxId?: string
  mcpServerIds?: string[]
  toolIds?: string[]
  automationIds?: string[]
}

export type Operation = {
  id: string
  name: string
  status: 'planning' | 'active' | 'completed' | 'compromised'
  priority: 'low' | 'medium' | 'high' | 'critical'
  location: string
  agents: number
  progress: number
  startDate: string
  estimatedCompletion: string
  description: string
}

// Mock data
const agents: Agent[] = [
  { id: "G-078W", name: "VENGEFUL SPIRIT", status: "active", location: "Berlin", lastSeen: "2 min ago", missions: 47, risk: "high" },
  { id: "G-079X", name: "OBSIDIAN SENTINEL", status: "standby", location: "Tokyo", lastSeen: "15 min ago", missions: 32, risk: "medium" },
  { id: "G-080Y", name: "GHOSTLY FURY", status: "active", location: "Cairo", lastSeen: "1 min ago", missions: 63, risk: "high" },
  { id: "G-081Z", name: "CURSED REVENANT", status: "compromised", location: "Moscow", lastSeen: "3 hours ago", missions: 28, risk: "critical" },
  { id: "G-082A", name: "VENOMOUS SHADE", status: "active", location: "London", lastSeen: "5 min ago", missions: 41, risk: "medium" },
  { id: "G-083B", name: "MYSTIC ENIGMA", status: "training", location: "Base Alpha", lastSeen: "1 day ago", missions: 12, risk: "low" },
]

const operations: Operation[] = [
  {
    id: "OP-OMEGA-001",
    name: "SHADOW PROTOCOL",
    status: "active",
    priority: "critical",
    location: "Eastern Europe",
    agents: 5,
    progress: 75,
    startDate: "2025-06-15",
    estimatedCompletion: "2025-06-30",
    description: "Track high-value target in Eastern Europe",
  },
]

export function listAgents(): Agent[] {
  return agents
}

export function getAgent(agentId: string): Agent | null {
  return agents.find(agent => agent.id === agentId) || null
}

export function setAgentStatus(agentId: string, status: AgentStatus): { success: boolean; message: string } {
  const agent = agents.find(a => a.id === agentId)
  if (!agent) {
    return { success: false, message: `Agent ${agentId} not found` }
  }

  agent.status = status
  agent.lastSeen = 'just now'

  return { success: true, message: `Agent ${agentId} status updated to ${status}` }
}

export function createOperation(
  name: string,
  location: string,
  priority: Operation['priority'],
  agentCount: number,
  description: string
): Operation {
  const operation: Operation = {
    id: `OP-${Math.random().toString(36).substr(2, 9).toUpperCase()}`,
    name: name.toUpperCase(),
    status: 'planning',
    priority,
    location,
    agents: agentCount,
    progress: 0,
    startDate: new Date().toISOString().split('T')[0],
    estimatedCompletion: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
    description,
  }
  operations.push(operation)
  return operation
}

export function updateAgentAssignments(agentId: string, partial: Partial<Pick<Agent, 'sandboxId' | 'mcpServerIds' | 'toolIds' | 'automationIds'>>): { success: boolean; message: string } {
  const agent = agents.find(a => a.id === agentId)
  if (!agent) return { success: false, message: `Agent ${agentId} not found` }
  if (partial.sandboxId !== undefined) agent.sandboxId = partial.sandboxId
  if (partial.mcpServerIds !== undefined) agent.mcpServerIds = partial.mcpServerIds
  if (partial.toolIds !== undefined) agent.toolIds = partial.toolIds
  if (partial.automationIds !== undefined) agent.automationIds = partial.automationIds
  return { success: true, message: 'Updated' }
}

